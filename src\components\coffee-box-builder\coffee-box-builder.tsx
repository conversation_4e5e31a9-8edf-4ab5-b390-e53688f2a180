'use client'

import { useState, useEffect, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatCurrency } from '@/lib/utils'
import { Plus, Minus, ShoppingCart, Coffee } from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/use-toast'
import { CartManager } from '@/lib/cart'
import { useTranslations, useLocale } from 'next-intl'

interface Product {
  id: string
  title: string
  description: string
  category: string
  coffee_type: string | null
  brand: string | null
  blend: string | null
  machine_compatibility: string[] | null
  pack_quantity: number | null
  pack_weight_grams: number | null
  price: number
  discount_price: number | null
  cost_per_espresso: number | null
  images: string[]
  is_available: boolean
}

interface CoffeeBoxItem {
  product: Product
  quantity: number
}

interface CoffeeBoxBuilderProps {
  products: Product[]
}

export function CoffeeBoxBuilder({ products }: CoffeeBoxBuilderProps) {
  const [modalProduct, setModalProduct] = useState<Product | null>(null)
  const [selectedItems, setSelectedItems] = useState<CoffeeBoxItem[]>([])
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterBrand, setFilterBrand] = useState<string>('all')
  const [filterBlend, setFilterBlend] = useState<string>('all')
  const [filterCompatibility, setFilterCompatibility] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(false)
  const [userPoints, setUserPoints] = useState(0)
  const supabase = createClient()
  const locale = useLocale()
  const [giftThresholds, setGiftThresholds] = useState<{ threshold_points: number; gift_product_ids: string[] }[]>([])

  useEffect(() => {
    const loadGiftThresholds = async () => {
      const { data, error } = await supabase
        .from('gift_thresholds')
        .select('threshold_points, gift_product_ids')
        .eq('is_active', true)
        .order('threshold_points', { ascending: true })
      if (!error && data) setGiftThresholds(data as { threshold_points: number; gift_product_ids: string[] }[])
    }
    loadGiftThresholds()
  }, [supabase])

  // Load current user and their points
  useEffect(() => {
    const loadUserAndPoints = async () => {
      try {
        const response = await fetch('/api/user/points')
        const data = await response.json()
        setUserPoints(data.points || 0)
      } catch (error) {
        console.error('Error loading user points:', error)
        setUserPoints(0)
      }
    }
    loadUserAndPoints()
  }, [])
  const router = useRouter()
  const { toast } = useToast()
  const t = useTranslations('common')
  const tc = useTranslations('cart')
  const tb = useTranslations('coffeeBoxBuilder')
  const ts = useTranslations('shop')

  // Filter products
  const handleOpenProduct = (product: Product) => setModalProduct(product)
  const closeModal = () => setModalProduct(null)

  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      if (filterCategory !== 'all' && product.category !== filterCategory) return false
      if (filterType !== 'all' && product.coffee_type !== filterType) return false
      if (filterBrand !== 'all' && product.brand !== filterBrand) return false
      if (filterBlend !== 'all' && product.blend !== filterBlend) return false
      if (filterCompatibility !== 'all' && product.machine_compatibility &&
          !product.machine_compatibility.some(compat =>
            compat.toLowerCase().includes(filterCompatibility.toLowerCase())
          )) return false
      return true
    })
  }, [products, filterCategory, filterType, filterBrand, filterBlend, filterCompatibility])

  // Get unique types and brands for filters
  const coffeeTypes = useMemo(() => {
    const types = [...new Set(products.map(p => p.coffee_type).filter(Boolean))] as string[]
    return types.sort()
  }, [products])

  const brands = useMemo(() => {
    const brandList = [...new Set(products.map(p => p.brand).filter(Boolean))] as string[]
    return brandList.sort()
  }, [products])

  const categories = useMemo(() => {
    const cats = [...new Set(products.map(p => p.category).filter(Boolean))] as string[]
    return cats.sort()
  }, [products])

  const blends = useMemo(() => {
    const blendList = [...new Set(products.map(p => p.blend).filter(Boolean))] as string[]
    return blendList.sort()
  }, [products])

  const compatibilities = useMemo(() => {
    const compatList = [...new Set(
      products
        .filter(p => p.machine_compatibility && p.machine_compatibility.length > 0)
        .flatMap(p => p.machine_compatibility || [])
        .filter(Boolean)
    )] as string[]
    return compatList.sort()
  }, [products])

  // Calculate totals
  const totals = useMemo(() => {
    const subtotal = selectedItems.reduce((sum, item) => {
      const price = item.product.discount_price || item.product.price
      return sum + (price * item.quantity)
    }, 0)

    const freeShippingThreshold = 90
    const shippingCost = subtotal >= freeShippingThreshold ? 0 : 8.90
    const total = subtotal + shippingCost

    // Points to next level
    const POINTS_PER_CHF = 1
    const lifetimePoints = userPoints + subtotal * POINTS_PER_CHF
    const levelPointThresholds = [200, 500, 1000] // Updated to match new point-based levels
    const nextLevelPoints = levelPointThresholds.find(th => lifetimePoints < th)
    const pointsProgress = nextLevelPoints ? (lifetimePoints / nextLevelPoints) * 100 : 100
    const remainingPoints = nextLevelPoints ? nextLevelPoints - lifetimePoints : 0

    // Gift thresholds based on user points (not order amount)
    const sortedThresholds = giftThresholds.map(t => t.threshold_points).sort((a, b) => a - b)
    const nextGiftThreshold = sortedThresholds.find(points => userPoints < points)
    const giftProgress = nextGiftThreshold ? (userPoints / nextGiftThreshold) * 100 : 100

    return {
      subtotal,
      shippingCost,
      total,
      nextGiftThreshold,
      giftProgress,
      freeShippingProgress: Math.min((subtotal / freeShippingThreshold) * 100, 100),
      nextLevelPoints,
      pointsProgress,
      remainingPoints
    }
  }, [selectedItems, giftThresholds, userPoints])

  const nextGiftProduct = useMemo(() => {
    if (!totals.nextGiftThreshold) return null
    const thresholdObj = giftThresholds.find(t => t.threshold_points === totals.nextGiftThreshold)
    if (thresholdObj) {
      const productId = thresholdObj.gift_product_ids[0]
      return products.find(p => p.id === productId) || null
    }
    return null
  }, [giftThresholds, totals.nextGiftThreshold, products])

  const addProduct = (product: Product) => {
    setSelectedItems(prev => {
      const existing = prev.find(item => item.product.id === product.id)
      if (existing) {
        return prev.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      } else {
        return [...prev, { product, quantity: 1 }]
      }
    })
  }

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      setSelectedItems(prev => prev.filter(item => item.product.id !== productId))
    } else {
      setSelectedItems(prev =>
        prev.map(item =>
          item.product.id === productId
            ? { ...item, quantity }
            : item
        )
      )
    }
  }

  const addToCart = async () => {
    if (selectedItems.length === 0) {
      toast({
        title: tb('noProductsSelected'),
        description: tb('selectAtLeastOne'),
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    const cartManager = new CartManager()

    try {
      let allSuccess = true
      for (const item of selectedItems) {
        const success = await cartManager.addToCart(item.product.id, item.quantity)
        if (!success) {
          allSuccess = false
          break
        }
      }

      if (allSuccess) {
        toast({
          title: t('coffeeBoxAdded'),
          description: t('productsAddedToCart', { count: selectedItems.length }),
        })
        setSelectedItems([])
        router.push(`/${locale}/cart`)
      } else {
        throw new Error('Failed to add some items to cart')
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast({
        title: t('error'),
        description: t('coffeeBoxAddError'),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
    <div className="grid lg:grid-cols-3 gap-8">
      {/* Product Selection */}
      <div className="lg:col-span-2 space-y-6">
        {/* Filters */}
        <Card className="border-0 bg-gradient-to-r from-white to-gray-50/50 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Coffee className="h-5 w-5 text-amber-600" />
              {tb('filters.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {nextGiftProduct && (
              <div className="mb-2 text-sm font-medium">
                {nextGiftProduct.title}
              </div>
            )}
            <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.category')}</label>
                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allCategories')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allCategories')}</SelectItem>
                    {categories.map(category => (
                       <SelectItem key={category} value={category}>
                         {ts(category, { defaultValue: category })}
                       </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.coffeeType')}</label>
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allTypes')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allTypes')}</SelectItem>
                    {coffeeTypes.map(type => {
                      const translatedType = tb(`coffeeTypes.${type}`, { defaultValue: type })
                      return (
                        <SelectItem key={type} value={type}>
                          {translatedType}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.brand')}</label>
                <Select value={filterBrand} onValueChange={setFilterBrand}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allBrands')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allBrands')}</SelectItem>
                    {brands.map(brand => (
                      <SelectItem key={brand} value={brand}>
                        {brand}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.blend')}</label>
                <Select value={filterBlend} onValueChange={setFilterBlend}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allBlends')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allBlends')}</SelectItem>
                    {blends.map(blend => (
                      <SelectItem key={blend} value={blend}>
                        {blend}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.compatibility')}</label>
                <Select value={filterCompatibility} onValueChange={setFilterCompatibility}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allCompatibilities')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allCompatibilities')}</SelectItem>
                    {compatibilities.map(compatibility => (
                      <SelectItem key={compatibility} value={compatibility}>
                        {compatibility}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Products Grid */}
        <div className="grid md:grid-cols-2 gap-6">
          {filteredProducts.map(product => {
            const selectedItem = selectedItems.find(item => item.product.id === product.id)

            return (
              <Card 
                key={product.id} 
                className="group relative cursor-pointer overflow-hidden border-0 bg-gradient-to-br from-white to-gray-50/80 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                onClick={() => handleOpenProduct(product)}
              >
                <CardContent className="p-0">
                  <div className="relative">
                    {/* Background gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    
                    {/* Discount badge */}
                    {product.discount_price && (
                      <div className="absolute top-3 right-3 z-10">
                        <Badge className="bg-red-500 text-white text-xs font-medium px-2 py-1 shadow-lg">
                          -{Math.round(((product.price - product.discount_price) / product.price) * 100)}%
                        </Badge>
                      </div>
                    )}

                    <div className="flex gap-4 p-6">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden shadow-sm group-hover:shadow-md transition-shadow duration-300">
                          {product.images && product.images[0] ? (
                            <Image
                              src={product.images[0]}
                              alt={product.title}
                              width={80}
                              height={80}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-amber-50 to-amber-100">
                              <Coffee className="h-8 w-8 text-amber-600/70" />
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <div className="mb-3">
                          <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2 group-hover:text-amber-700 transition-colors duration-200">
                            {product.title}
                          </h3>
                          {product.brand && (
                            <p className="text-sm text-gray-500 font-medium">{product.brand}</p>
                          )}
                        </div>

                        {/* Product badges */}
                        <div className="flex items-center gap-2 mb-4 flex-wrap">
                          {product.coffee_type && (
                            <Badge variant="secondary" className="text-xs bg-amber-50 text-amber-700 border-amber-200 font-medium">
                              {tb(`coffeeTypes.${product.coffee_type}`, { defaultValue: product.coffee_type })}
                            </Badge>
                          )}
                          {(product.coffee_type === 'capsules' || product.coffee_type === 'pods') && product.pack_quantity && (
                            <Badge variant="outline" className="text-xs border-gray-300 text-gray-600">
                              {product.pack_quantity} {ts('product.pieces')}
                            </Badge>
                          )}
                          {(product.coffee_type === 'ground' || product.coffee_type === 'beans') && product.pack_weight_grams && (
                            <Badge variant="outline" className="text-xs border-gray-300 text-gray-600">
                              {product.pack_weight_grams} g
                            </Badge>
                          )}
                        </div>
                        
                        {/* Price and actions */}
                        <div className="flex items-end justify-between">
                          <div className="space-y-1">
                            {product.discount_price ? (
                              <div className="space-y-1">
                                <div className="text-sm text-gray-400 line-through font-medium">
                                  {formatCurrency(product.price)}
                                </div>
                                <div className="font-bold text-lg text-gray-900">
                                  {formatCurrency(product.discount_price)}
                                </div>
                              </div>
                            ) : (
                              <div className="font-bold text-lg text-gray-900">
                                {formatCurrency(product.price)}
                              </div>
                            )}
                            {product.cost_per_espresso && (
                              <div className="text-xs text-emerald-600 font-medium bg-emerald-50 px-2 py-1 rounded-full inline-block">
                                {formatCurrency(product.cost_per_espresso)}/{ts('product.perEspresso')}
                              </div>
                            )}
                          </div>
                          
                          {/* Quantity controls or add button */}
                          {selectedItem ? (
                            <div className="flex items-center gap-2 bg-white rounded-lg p-1 shadow-sm border border-gray-200">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => { e.stopPropagation(); updateQuantity(product.id, selectedItem.quantity - 1) }}
                                className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 transition-colors duration-200"
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <span className="text-sm font-semibold px-3 py-1 bg-gray-50 rounded text-gray-900 min-w-[2rem] text-center">
                                {selectedItem.quantity}
                              </span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => { e.stopPropagation(); updateQuantity(product.id, selectedItem.quantity + 1) }}
                                className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600 transition-colors duration-200"
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => { e.stopPropagation(); addProduct(product) }}
                              className="h-9 px-4 bg-white hover:bg-amber-50 hover:border-amber-300 hover:text-amber-700 transition-all duration-200 shadow-sm"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              {t('add')}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Summary Sidebar */}
      <div className="space-y-6">
        {/* Progress Indicators */}
        {totals.subtotal < 50 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">{tc('freeShipping')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span>{tb('remaining', { amount: formatCurrency(50 - totals.subtotal) })}</span>
                  <span>{Math.round(totals.freeShippingProgress)}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all"
                    style={{ width: `${totals.freeShippingProgress}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {totals.nextGiftThreshold && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">{tb('nextGift')}</CardTitle>
            </CardHeader>
            <CardContent>
              {nextGiftProduct && (
                <div className="mb-2 text-sm font-medium">
                  {nextGiftProduct.title}
                </div>
              )}
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span>{tb('remaining', { amount: formatCurrency(totals.nextGiftThreshold - totals.subtotal) })}</span>
                  <span>{Math.round(totals.giftProgress)}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all"
                    style={{ width: `${totals.giftProgress}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {totals.nextLevelPoints && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">{tb('nextLevel', { defaultValue: 'Prossimo livello' })}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span>{tb('remainingPoints', { amount: totals.remainingPoints })}</span>
                  <span>{Math.round(totals.pointsProgress)}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-yellow-500 h-2 rounded-full transition-all"
                    style={{ width: `${totals.pointsProgress}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Order Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">{tb('summary.title')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>{tc('subtotal')}</span>
              <span>{formatCurrency(totals.subtotal)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{tc('shipping')}</span>
              <span>
                {totals.shippingCost === 0 ? (
                  <span className="text-green-600 font-medium">{tc('free')}</span>
                ) : (
                  formatCurrency(totals.shippingCost)
                )}
              </span>
            </div>
            <div className="border-t pt-3">
              <div className="flex justify-between font-bold">
                <span>{tc('total')}</span>
                <span>{formatCurrency(totals.total)}</span>
              </div>
            </div>
            
            <Button 
              onClick={addToCart}
              disabled={selectedItems.length === 0 || isLoading}
              className="w-full"
              size="lg"
            >
              <ShoppingCart className="mr-2 h-4 w-4" />
              {isLoading ? t('adding') : t('addToCart')}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>

    {/* Product Detail Modal */}
    <Dialog open={!!modalProduct} onOpenChange={(open) => { if (!open) closeModal() }}>
      {modalProduct && (
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-8 m-4">
          <DialogHeader className="space-y-4 pb-8 pr-8">
            <div className="flex items-start justify-between gap-8">
              <div className="space-y-3 flex-1 min-w-0">
                <DialogTitle className="text-2xl font-bold text-gray-900 leading-tight">
                  {modalProduct.title}
                </DialogTitle>
                {modalProduct.brand && (
                  <p className="text-lg text-gray-600 font-medium">{modalProduct.brand}</p>
                )}
                <div className="flex items-center gap-2 flex-wrap">
                  <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                    {ts(modalProduct.category, { defaultValue: modalProduct.category })}
                  </Badge>
                  {modalProduct.coffee_type && (
                    <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
                      {tb(`coffeeTypes.${modalProduct.coffee_type}`, { defaultValue: modalProduct.coffee_type })}
                    </Badge>
                  )}
                  {modalProduct.blend && (
                    <Badge variant="outline" className="border-green-300 text-green-700">
                      {modalProduct.blend}
                    </Badge>
                  )}
                  {!modalProduct.is_available && (
                    <Badge variant="destructive">
                      {t('unavailable')}
                    </Badge>
                  )}
                </div>
              </div>
              
              {/* Price section */}
              <div className="text-right space-y-2 flex-shrink-0 min-w-[160px]">
                {modalProduct.discount_price ? (
                  <div className="space-y-1">
                    <div className="text-lg text-gray-400 line-through font-medium">
                      {formatCurrency(modalProduct.price)}
                    </div>
                    <div className="text-3xl font-bold text-gray-900">
                      {formatCurrency(modalProduct.discount_price)}
                    </div>
                    <div className="inline-flex items-center px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">
                      -{Math.round(((modalProduct.price - modalProduct.discount_price) / modalProduct.price) * 100)}% OFF
                    </div>
                  </div>
                ) : (
                  <div className="text-3xl font-bold text-gray-900">
                    {formatCurrency(modalProduct.price)}
                  </div>
                )}
                {modalProduct.cost_per_espresso && (
                  <div className="text-sm text-emerald-600 font-medium bg-emerald-50 px-3 py-1 rounded-full">
                    {formatCurrency(modalProduct.cost_per_espresso)}/{ts('product.perEspresso')}
                  </div>
                )}
              </div>
            </div>
          </DialogHeader>
          
          <div className="grid md:grid-cols-2 gap-8">
            {/* Image Gallery */}
            <div className="space-y-4">
              <div className="aspect-square rounded-xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 shadow-lg">
                {modalProduct.images && modalProduct.images[0] ? (
                  <Image
                    src={modalProduct.images[0]}
                    alt={modalProduct.title}
                    width={500}
                    height={500}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-amber-50 to-amber-100">
                    <Coffee className="h-16 w-16 text-amber-600/50" />
                  </div>
                )}
              </div>
              
              {/* Additional images */}
              {modalProduct.images && modalProduct.images.length > 1 && (
                <div className="grid grid-cols-4 gap-2">
                  {modalProduct.images.slice(1, 5).map((image, index) => (
                    <div key={index} className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                      <Image
                        src={image}
                        alt={`${modalProduct.title} ${index + 2}`}
                        width={100}
                        height={100}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="space-y-6">
              {/* Description */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">Descrizione</h3>
                <DialogDescription className="text-gray-600 leading-relaxed whitespace-pre-line">
                  {modalProduct.description}
                </DialogDescription>
              </div>

              {/* Product Specifications */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">Specifiche</h3>
                <div className="grid gap-3">
                  {modalProduct.pack_weight_grams && (
                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Peso confezione</span>
                      <span className="font-medium text-gray-900">{modalProduct.pack_weight_grams} g</span>
                    </div>
                  )}
                  {modalProduct.pack_quantity && (
                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Quantità</span>
                      <span className="font-medium text-gray-900">{modalProduct.pack_quantity} {ts('product.pieces')}</span>
                    </div>
                  )}
                  {modalProduct.coffee_type && (
                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Tipo di caffè</span>
                      <span className="font-medium text-gray-900">
                        {tb(`coffeeTypes.${modalProduct.coffee_type}`, { defaultValue: modalProduct.coffee_type })}
                      </span>
                    </div>
                  )}
                  {modalProduct.blend && (
                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Miscela</span>
                      <span className="font-medium text-gray-900">{modalProduct.blend}</span>
                    </div>
                  )}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Categoria</span>
                    <span className="font-medium text-gray-900">
                      {ts(modalProduct.category, { defaultValue: modalProduct.category })}
                    </span>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span className="text-gray-600">Disponibilità</span>
                    <span className={`font-medium ${modalProduct.is_available ? 'text-green-600' : 'text-red-600'}`}>
                      {modalProduct.is_available ? 'Disponibile' : 'Non disponibile'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Action Button */}
              <div className="pt-4 border-t">
                <Button 
                  onClick={(e) => { e.stopPropagation(); addProduct(modalProduct); closeModal(); }}
                  disabled={!modalProduct.is_available}
                  className="w-full h-12 text-lg font-medium bg-amber-600 hover:bg-amber-700 disabled:bg-gray-300"
                  size="lg"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  {modalProduct.is_available ? t('add') : t('unavailable')}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      )}
    </Dialog>
    </>
  )
}
